# ✈️ Google Flights Clone

A modern, responsive flight search application built with React and Material-UI, powered by the Sky-Scrapper API from RapidAPI. This application replicates the Google Flights interface with a clean, user-friendly design.

![Flight Search Interface](googleflightlist.png)

## 🚀 Features

- **Airport Search**: Intelligent autocomplete search for airports worldwide
- **Flight Search**: Comprehensive flight search with multiple filters
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Real-time Data**: Live flight information from Sky-Scrapper API
- **Modern UI**: Clean Material-UI components with Google Flights-inspired design
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Loading States**: Smooth loading animations and progress indicators

## 🛠️ Tech Stack

- **Frontend**: React 19 with Hooks
- **UI Library**: Material-UI (MUI) v7
- **Build Tool**: Vite
- **HTTP Client**: Axios
- **API**: Sky-Scrapper (RapidAPI)
- **Styling**: Material-UI with custom theme
- **State Management**: React useState/useEffect

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd google-flight-clone
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` and add your RapidAPI key:
   ```env
   VITE_RAPIDAPI_KEY=your_rapidapi_key_here
   VITE_RAPIDAPI_HOST=sky-scrapper.p.rapidapi.com
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:5173`

## 🔑 API Setup

1. Sign up for a [RapidAPI account](https://rapidapi.com/)
2. Subscribe to the [Sky-Scrapper API](https://rapidapi.com/apiheya/api/sky-scrapper)
3. Copy your API key from the RapidAPI dashboard
4. Add the key to your `.env` file

## 🏗️ Project Structure

```
src/
├── api/
│   ├── axiosConfig.js      # Axios configuration with interceptors
│   ├── fetchAirports.js    # Airport search API calls
│   └── fetchFlights.js     # Flight search API calls
├── components/
│   ├── AirportAutocomplete.jsx  # Airport search component
│   ├── SearchBar.jsx           # Main search interface
│   ├── FlightsTable.jsx        # Flight results table
│   ├── LoadingSpinner.jsx      # Loading components
│   └── ErrorBoundary.jsx       # Error handling
├── utils/
│   ├── constants.js        # App constants
│   ├── formatDate.js       # Date formatting utilities
│   └── formatDuration.js   # Duration and price formatting
├── App.jsx                 # Main application component
└── main.jsx               # Application entry point
```

## 🎨 Features Overview

### Airport Search
- Debounced search with 300ms delay
- Real-time airport suggestions
- Support for city and airport names
- Keyboard navigation support

### Flight Search
- Origin and destination selection
- Date picker with validation
- Cabin class selection
- Passenger count configuration
- Comprehensive search parameters

### Results Display
- Responsive table/card layout
- Airline logos and information
- Flight duration and stops
- Price comparison
- Pagination support

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 📱 Responsive Design

The application is fully responsive with breakpoints:
- Mobile: < 600px
- Tablet: 600px - 960px
- Desktop: > 960px

## 🚨 Error Handling

- API error handling with user-friendly messages
- Error boundaries for component crashes
- Network error detection
- Validation for search parameters

## 🔒 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `VITE_RAPIDAPI_KEY` | Your RapidAPI key for Sky-Scrapper | Yes |
| `VITE_RAPIDAPI_HOST` | API host (default: sky-scrapper.p.rapidapi.com) | No |

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📞 Support

For support, please open an issue in the GitHub repository.
