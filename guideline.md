# ✈️ Google Flights Clone (<PERSON>act + MUI)

I’m building a responsive, modern flight search frontend app using **React** and **Material UI**, based on the Google Flights layout and powered by the **Sky-Scrapper API** from RapidAPI.

Please help me generate production-level code and components based on the following specifications.

---

## ✅ Project Goal

- Build a Google Flights–like interface
- Search for flights between two airports (origin and destination)
- Use autocomplete search for airport names
- Display paginated flight data in a Material UI table
- Complete within 16 hours and deliver clean, performant frontend code

---

## 🔧 Tech Stack

| Feature               | Tool/Library              |
|------------------------|---------------------------|
| Framework              | React (Hooks, Vite/CRA)   |
| UI Library             | Material UI (v5)          |
| HTTP Requests          | Axios                     |
| API Source             | Sky-Scrapper (RapidAPI)   |
| Debounce               | lodash.debounce           |
| Styling                | MUI with `sx` props       |
| State Management       | useState / useEffect      |
| Environment Vars       | `.env` via `VITE_` prefix |
| Pagination             | MUI Table + Pagination    |

---

## 📁 Folder Structure

```
/src
├── /api
│ ├── axiosConfig.js
│ ├── fetchAirports.js
│ └── fetchFlights.js
├── /components
│ ├── AirportAutocomplete.jsx
│ ├── SearchBar.jsx
│ ├── FlightsTable.jsx
│ ├── LoadingSpinner.jsx
├── /utils
│ ├── formatDate.js
│ ├── formatDuration.js
│ └── constants.js
├── App.jsx
├── index.js
└── styles.css

```

---

## 🔗 APIs Used

### ✈️ 1. Search Airports (Autocomplete)

**Endpoint:**
`GET https://sky-scrapper.p.rapidapi.com/api/v1/flights/searchAirport?query=New+York&locale=en-US
`

**Returns:**

```json
{
  "status": true,
  "timestamp": 1753884869989,
  "data": [
    {
      "skyId": "NYCA",
      "entityId": "27537542",
      "presentation": {
        "title": "New York",
        "suggestionTitle": "New York (Any)",
        "subtitle": "United States"
      },
      "navigation": {
        "entityId": "27537542",
        "entityType": "CITY",
        "localizedName": "New York",
        "relevantFlightParams": {
          "skyId": "NYCA",
          "entityId": "27537542",
          "flightPlaceType": "CITY",
          "localizedName": "New York"
        },
        "relevantHotelParams": {
          "entityId": "27537542",
          "entityType": "CITY",
          "localizedName": "New York"
        }
      }
    },
    {
      "skyId": "JFK",
      "entityId": "95565058",
      "presentation": {
        "title": "New York John F. Kennedy",
        "suggestionTitle": "New York John F. Kennedy (JFK)",
        "subtitle": "United States"
      },
      "navigation": {
        "entityId": "95565058",
        "entityType": "AIRPORT",
        "localizedName": "New York John F. Kennedy",
        "relevantFlightParams": {
          "skyId": "JFK",
          "entityId": "95565058",
          "flightPlaceType": "AIRPORT",
          "localizedName": "New York John F. Kennedy"
        },
        "relevantHotelParams": {
          "entityId": "27537542",
          "entityType": "CITY",
          "localizedName": "New York"
        }
      }
    },
    {
      "skyId": "EWR",
      "entityId": "95565059",
      "presentation": {
        "title": "New York Newark",
        "suggestionTitle": "New York Newark (EWR)",
        "subtitle": "United States"
      },
      "navigation": {
        "entityId": "95565059",
        "entityType": "AIRPORT",
        "localizedName": "New York Newark",
        "relevantFlightParams": {
          "skyId": "EWR",
          "entityId": "95565059",
          "flightPlaceType": "AIRPORT",
          "localizedName": "New York Newark"
        },
        "relevantHotelParams": {
          "entityId": "27537542",
          "entityType": "CITY",
          "localizedName": "New York"
        }
      }
    },
    {
      "skyId": "LGA",
      "entityId": "95565057",
      "presentation": {
        "title": "New York LaGuardia",
        "suggestionTitle": "New York LaGuardia (LGA)",
        "subtitle": "United States"
      },
      "navigation": {
        "entityId": "95565057",
        "entityType": "AIRPORT",
        "localizedName": "New York LaGuardia",
        "relevantFlightParams": {
          "skyId": "LGA",
          "entityId": "95565057",
          "flightPlaceType": "AIRPORT",
          "localizedName": "New York LaGuardia"
        },
        "relevantHotelParams": {
          "entityId": "27537542",
          "entityType": "CITY",
          "localizedName": "New York"
        }
      }
    },
    {
      "skyId": "SWF",
      "entityId": "95566280",
      "presentation": {
        "title": "Stewart International",
        "suggestionTitle": "Stewart International (SWF)",
        "subtitle": "United States"
      },
      "navigation": {
        "entityId": "95566280",
        "entityType": "AIRPORT",
        "localizedName": "Stewart International",
        "relevantFlightParams": {
          "skyId": "SWF",
          "entityId": "95566280",
          "flightPlaceType": "AIRPORT",
          "localizedName": "Stewart International"
        },
        "relevantHotelParams": {
          "entityId": "27537542",
          "entityType": "CITY",
          "localizedName": "New York"
        }
      }
    },
    {
      "skyId": "BUF",
      "entityId": "95673358",
      "presentation": {
        "title": "Buffalo Niagara",
        "suggestionTitle": "Buffalo Niagara (BUF)",
        "subtitle": "United States"
      },
      "navigation": {
        "entityId": "95673358",
        "entityType": "AIRPORT",
        "localizedName": "Buffalo Niagara",
        "relevantFlightParams": {
          "skyId": "BUF",
          "entityId": "95673358",
          "flightPlaceType": "AIRPORT",
          "localizedName": "Buffalo Niagara"
        },
        "relevantHotelParams": {
          "entityId": "27539605",
          "entityType": "CITY",
          "localizedName": "Buffalo"
        }
      }
    },
    {
      "skyId": "ALB",
      "entityId": "95674229",
      "presentation": {
        "title": "Albany",
        "suggestionTitle": "Albany (ALB)",
        "subtitle": "United States"
      },
      "navigation": {
        "entityId": "95674229",
        "entityType": "AIRPORT",
        "localizedName": "Albany",
        "relevantFlightParams": {
          "skyId": "ALB",
          "entityId": "95674229",
          "flightPlaceType": "AIRPORT",
          "localizedName": "Albany"
        },
        "relevantHotelParams": {
          "entityId": "27536537",
          "entityType": "CITY",
          "localizedName": "Albany"
        }
      }
    },
    {
      "skyId": "SYR",
      "entityId": "95674004",
      "presentation": {
        "title": "Syracuse",
        "suggestionTitle": "Syracuse (SYR)",
        "subtitle": "United States"
      },
      "navigation": {
        "entityId": "95674004",
        "entityType": "AIRPORT",
        "localizedName": "Syracuse",
        "relevantFlightParams": {
          "skyId": "SYR",
          "entityId": "95674004",
          "flightPlaceType": "AIRPORT",
          "localizedName": "Syracuse"
        },
        "relevantHotelParams": {
          "entityId": "27547103",
          "entityType": "CITY",
          "localizedName": "Syracuse"
        }
      }
    }
  ]
}
```

Only include airports where navigation.entityType === "AIRPORT"

## 🛫 2. Search Flight

**Endpoint:**
`GET https://sky-scrapper.p.rapidapi.com/api/v2/flights/searchFlights?originSkyId=LOND&destinationSkyId=NYCA&originEntityId=27544008&destinationEntityId=27537542&date=2025-07-31&cabinClass=economy&adults=1&childrens=0&infants=0&sortBy=price_high&limit=2&currency=USD&market=en-US&countryCode=US`

**Query Params:**

- originSkyId *
eg:LOND (String)

The originSkyId code can be extracted from the Search Airport API in the Flights collection.

- destinationSkyId*
eg: NYCA(String)

The destinationSkyId code can be extracted from the Search Airport API in the Flights collection.

- originEntityId*
eg:27544008 (String)

The originEntityId code can be extracted from the Search Airport API in the Flights collection.

- destinationEntityId*
eg: 27537542 (String)

The destinationEntityId code can be extracted from the Search Airport API in the Flights collection.

- date*
eg: 2025-07-16 (String)
Date (yyyy-mm-dd)

Departure or travel date. Format: YYYY-MM-DD

- returnDate (optional)
Date (yyyy-mm-dd)
Return date. Format: YYYY-MM-DD

- cabinClass (optional)
eg: economy
Enum
Cabin class Ex: economy Default value: economy

economy: Economy,
premium_economy: Premium Economy,
business: Business,
first: First

- adults (optional)
1
Number
Adults: 12+ years,
Default value: 1,
Ex: 2

Default: 1

- childrens (optional)
0
Number
Children: 2-12 years,
Default value: 0,
Ex: 2

Default: 0

- infants (optional)
0
Number
Infants: Under 2 years,Default value: 0,
Ex: 1

Default: 0

- sortBy (optional)
price_high

Enum
Sort By Ex: best,
Default value: best

best : Best price_high : Cheapest fastest : Fastest outbound_take_off_time : Outbound Take Off Time outbound_landing_time : Outbound Landing Time return_take_off_time : Return Take Off Time return_landing_time : Return Landinf Time

- limit (optional)
2
Number

Set a limit on the amount of records. Example: 100

Default: 0

- carriersIds(optional)
String

Filter the flight itinerary data by the carrier.If there are multiple carriers that need to be passed, they should be passed in comma-separated format. Example: -32672,-31435

- currency(optional)
USD
String
currency can be retrieved from api/v1/getConfig endpoint(data->currency) Default value: USD Ex: USD

- market(optional)
en-US
String
market can be retrieved from api/v1/getConfig endpoint(data->market) Default value: en-US Ex: en-US

- countryCode
(optional)
US
String
countryCode can be retrieved from api/v1/getConfig endpoint(data->countryCode) Default value: US Ex: US

**Response contains:**

```json

{
  "status": true,
  "timestamp": 1753885514475,
  "data": {
    "context": {
      "status": "incomplete",
      "sessionId": "KLUv_WAyABUHABaRNCKgqQ1zVcWrGktlgrwlt_zcxMCYK3emyG44pz17JFTkaEtcKgAsACoA6l6vsrPUxuDjJvsZLI3L1Tqqty8qdWmhpuZVyLr15fdvnA9KIIWjMAsUzeyH26Qvl3O-9yL7tvuNcsVfDgnoQQdnYRhSBOwhmUQgDVtp8nopzubasz1O_iqUbrVle8W7ygkw7N9W-idmG5Xtc10FV91zr2pCVu39gwyIOQDGKQEJxFJQPNnTrpveGr89_X6rahvTJNn5Y8ZoNBwGZ2nkgu_PCvZqhc1dAwQABvmApcsVLwu8KTQ=",
      "totalResults": 10
    },
    "itineraries": [
      {
        "id": "13554-2507310815--32171-0-12712-2507311128",
        "price": {
          "raw": 1410,
          "formatted": "$1,410",
          "pricingOptionId": "nce6GawUfnqr"
        },
        "legs": [
          {
            "id": "13554-2507310815--32171-0-12712-2507311128",
            "origin": {
              "id": "LHR",
              "entityId": "95565050",
              "name": "London Heathrow",
              "displayCode": "LHR",
              "city": "London",
              "country": "United Kingdom",
              "isHighlighted": false
            },
            "destination": {
              "id": "JFK",
              "entityId": "95565058",
              "name": "New York John F. Kennedy",
              "displayCode": "JFK",
              "city": "New York",
              "country": "United States",
              "isHighlighted": false
            },
            "durationInMinutes": 493,
            "stopCount": 0,
            "isSmallestStops": false,
            "departure": "2025-07-31T08:15:00",
            "arrival": "2025-07-31T11:28:00",
            "timeDeltaInDays": 0,
            "carriers": {
              "marketing": [
                {
                  "id": -32171,
                  "alternateId": "B6",
                  "logoUrl": "https://logos.skyscnr.com/images/airlines/favicon/B6.png",
                  "name": "jetBlue"
                }
              ],
              "operationType": "fully_operated"
            },
            "segments": [
              {
                "id": "13554-12712-2507310815-2507311128--32171",
                "origin": {
                  "flightPlaceId": "LHR",
                  "displayCode": "LHR",
                  "parent": {
                    "flightPlaceId": "LOND",
                    "displayCode": "LON",
                    "name": "London",
                    "type": "City"
                  },
                  "name": "London Heathrow",
                  "type": "Airport",
                  "country": "United Kingdom"
                },
                "destination": {
                  "flightPlaceId": "JFK",
                  "displayCode": "JFK",
                  "parent": {
                    "flightPlaceId": "NYCA",
                    "displayCode": "NYC",
                    "name": "New York",
                    "type": "City"
                  },
                  "name": "New York John F. Kennedy",
                  "type": "Airport",
                  "country": "United States"
                },
                "departure": "2025-07-31T08:15:00",
                "arrival": "2025-07-31T11:28:00",
                "durationInMinutes": 493,
                "flightNumber": "2220",
                "marketingCarrier": {
                  "id": -32171,
                  "name": "jetBlue",
                  "alternateId": "B6",
                  "allianceId": 0,
                  "displayCode": "B6"
                },
                "operatingCarrier": {
                  "id": -32171,
                  "name": "jetBlue",
                  "alternateId": "B6",
                  "allianceId": 0,
                  "displayCode": "B6"
                },
                "transportMode": "TRANSPORT_MODE_FLIGHT"
              }
            ]
          }
        ],
        "isSelfTransfer": false,
        "isProtectedSelfTransfer": false,
        "farePolicy": {
          "isChangeAllowed": false,
          "isPartiallyChangeable": false,
          "isCancellationAllowed": false,
          "isPartiallyRefundable": false
        },
        "fareAttributes": {},
        "isMashUp": false,
        "hasFlexibleOptions": false,
        "score": 0.58423
      },
      {
        "id": "13554-2507311135--32480-0-12712-2507311425",
        "price": {
          "raw": 1139,
          "formatted": "$1,139",
          "pricingOptionId": "ER6yJPVXooOW"
        },
        "legs": [
          {
            "id": "13554-2507311135--32480-0-12712-2507311425",
            "origin": {
              "id": "LHR",
              "entityId": "95565050",
              "name": "London Heathrow",
              "displayCode": "LHR",
              "city": "London",
              "country": "United Kingdom",
              "isHighlighted": false
            },
            "destination": {
              "id": "JFK",
              "entityId": "95565058",
              "name": "New York John F. Kennedy",
              "displayCode": "JFK",
              "city": "New York",
              "country": "United States",
              "isHighlighted": false
            },
            "durationInMinutes": 470,
            "stopCount": 0,
            "isSmallestStops": false,
            "departure": "2025-07-31T11:35:00",
            "arrival": "2025-07-31T14:25:00",
            "timeDeltaInDays": 0,
            "carriers": {
              "marketing": [
                {
                  "id": -32480,
                  "alternateId": "BA",
                  "logoUrl": "https://logos.skyscnr.com/images/airlines/favicon/BA.png",
                  "name": "British Airways"
                }
              ],
              "operationType": "fully_operated"
            },
            "segments": [
              {
                "id": "13554-12712-2507311135-2507311425--32480",
                "origin": {
                  "flightPlaceId": "LHR",
                  "displayCode": "LHR",
                  "parent": {
                    "flightPlaceId": "LOND",
                    "displayCode": "LON",
                    "name": "London",
                    "type": "City"
                  },
                  "name": "London Heathrow",
                  "type": "Airport",
                  "country": "United Kingdom"
                },
                "destination": {
                  "flightPlaceId": "JFK",
                  "displayCode": "JFK",
                  "parent": {
                    "flightPlaceId": "NYCA",
                    "displayCode": "NYC",
                    "name": "New York",
                    "type": "City"
                  },
                  "name": "New York John F. Kennedy",
                  "type": "Airport",
                  "country": "United States"
                },
                "departure": "2025-07-31T11:35:00",
                "arrival": "2025-07-31T14:25:00",
                "durationInMinutes": 470,
                "flightNumber": "173",
                "marketingCarrier": {
                  "id": -32480,
                  "name": "British Airways",
                  "alternateId": "BA",
                  "allianceId": -32000,
                  "displayCode": "BA"
                },
                "operatingCarrier": {
                  "id": -32480,
                  "name": "British Airways",
                  "alternateId": "BA",
                  "allianceId": -32000,
                  "displayCode": "BA"
                },
                "transportMode": "TRANSPORT_MODE_FLIGHT"
              }
            ]
          }
        ],
        "isSelfTransfer": false,
        "isProtectedSelfTransfer": false,
        "farePolicy": {
          "isChangeAllowed": false,
          "isPartiallyChangeable": false,
          "isCancellationAllowed": false,
          "isPartiallyRefundable": false
        },
        "eco": {
          "ecoContenderDelta": 9.880858
        },
        "fareAttributes": {},
        "tags": [
          "shortest"
        ],
        "isMashUp": false,
        "hasFlexibleOptions": false,
        "score": 0.999
      }
    ],
    "messages": [],
    "filterStats": {
      "duration": {
        "min": 470,
        "max": 1300,
        "multiCityMin": 470,
        "multiCityMax": 1300
      },
      "total": 10,
      "hasCityOpenJaw": false,
      "multipleCarriers": {
        "minPrice": "$844",
        "rawMinPrice": null
      },
      "airports": [
        {
          "city": "London",
          "airports": [
            {
              "id": "LGW",
              "entityId": "95565051",
              "name": "London Gatwick"
            },
            {
              "id": "LHR",
              "entityId": "95565050",
              "name": "London Heathrow"
            }
          ]
        },
        {
          "city": "New York",
          "airports": [
            {
              "id": "JFK",
              "entityId": "95565058",
              "name": "New York John F. Kennedy"
            },
            {
              "id": "EWR",
              "entityId": "95565059",
              "name": "New York Newark"
            }
          ]
        }
      ],
      "carriers": [
        {
          "id": -32480,
          "alternateId": "BA",
          "logoUrl": "https://logos.skyscnr.com/images/airlines/favicon/BA.png",
          "name": "British Airways",
          "minPrice": "$1,131",
          "allianceId": -32000
        },
        {
          "id": -32317,
          "alternateId": "AY",
          "logoUrl": "https://logos.skyscnr.com/images/airlines/favicon/AY.png",
          "name": "Finnair",
          "minPrice": "$1,564",
          "allianceId": -32000
        },
        {
          "id": -32217,
          "alternateId": "FI",
          "logoUrl": "https://logos.skyscnr.com/images/airlines/favicon/FI.png",
          "name": "Icelandair",
          "allianceId": 0
        },
        {
          "id": -32171,
          "alternateId": "B6",
          "logoUrl": "https://logos.skyscnr.com/images/airlines/favicon/B6.png",
          "name": "jetBlue",
          "minPrice": "$1,410",
          "allianceId": 0
        },
        {
          "id": -31927,
          "alternateId": "AT",
          "logoUrl": "https://logos.skyscnr.com/images/airlines/favicon/AT.png",
          "name": "Royal Air Maroc",
          "minPrice": "$802",
          "allianceId": -32000
        },
        {
          "id": -31901,
          "alternateId": "SK",
          "logoUrl": "https://logos.skyscnr.com/images/airlines/favicon/SK.png",
          "name": "Scandinavian Airlines",
          "minPrice": "$902",
          "allianceId": -31998
        }
      ],
      "stopPrices": {
        "direct": {
          "isPresent": true,
          "formattedPrice": "$1,131",
          "rawPrice": 1131
        },
        "one": {
          "isPresent": true,
          "formattedPrice": "$802",
          "rawPrice": 802
        },
        "twoOrMore": {
          "isPresent": false
        }
      },
      "alliances": [
        {
          "id": -32000,
          "name": "OneWorld"
        },
        {
          "id": -31998,
          "name": "SkyTeam"
        }
      ]
    },
    "flightsSessionId": "fc4d9ded-993b-4590-b407-1c8481c962ea",
    "destinationImageUrl": "https://content.skyscnr.com/m/3719e8f4a5daf43d/original/Flights-Placeholder.jpg"
  }
}

```

🧠 Implementation Plan

1. AirportAutocomplete.jsx
Uses MUI Autocomplete

Debounced search input using lodash.debounce

Fetches suggestions from Sky-Scrapper

Autoselects first suggestion on Enter if none selected

2. SearchBar.jsx
Two AirportAutocomplete fields (origin, destination)

Date input

Button to trigger flight fetch

3. fetchAirports.js

const response = await axios.get('/flights/searchAirport', { params: { query } });
Returns filtered airports (entityType === 'AIRPORT').

4. fetchFlights.js

const response = await axios.get('/flights/searchFlights', { params });
Maps legs[0], price, carriers, stopCount, duration.

## 📊 FlightsTable.jsx
MUI Table

Paginated

Columns:

Airline (logo + name)

Route (e.g., LHR → JFK)

Time (e.g., 11:30 → 16:20)

Duration (e.g., 6h 15m)

Stops (Non-stop, 1 Stop, etc.)

Price ($1,420)

## 🧪 Optimization & Best Practices
- Feature Applied
- Axios config file ✅
- API key via .env ✅
- Separation of concerns (API/UI) ✅
- Debounced API calls ✅
- MUI responsive design ✅
- Modular reusable components ✅
- Pagination in table ✅
- Utility functions in utils/ ✅
- Error & loading handling ✅

📦 .env Example
```
VITE_RAPIDAPI_KEY=your_rapidapi_key_here
```
Used in axiosConfig.js via import.meta.env.

## ✅ Completion Guidelines

- Keep UI simple and elegant

- Focus on clean code and component structure

- Prioritize user experience (loading, error states, responsiveness)


## Instructions summary

- Set up React project using Vite or CRA.

- Add Axios config using .env.

- Create reusable components:

- AirportAutocomplete

- FlightsTable

- SearchBar

- Integrate the airport search and flight APIs.

- Display flights in paginated table.

- Apply MUI styling and layout.

- Optimize for performance and API call limits.
