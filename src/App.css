/* Google Flights inspired styles */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background-color: #ffffff;
  color: #202124;
  line-height: 1.5;
}

#root {
  min-height: 100vh;
  background-color: #ffffff;
}

/* Header styles */
.header {
  background-color: #ffffff;
  border-bottom: 1px solid #e8eaed;
  padding: 16px 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  font-size: 22px;
  font-weight: 400;
  color: #1a73e8;
  text-decoration: none;
}

/* Main container */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

/* Search section */
.search-section {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 1px 6px rgba(32, 33, 36, 0.1);
  padding: 24px;
  margin-bottom: 32px;
}

.search-title {
  font-size: 32px;
  font-weight: 400;
  color: #202124;
  margin-bottom: 24px;
  text-align: left;
}

/* Search form styles */
.search-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.form-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
  flex: 1;
}

.form-label {
  font-size: 12px;
  font-weight: 500;
  color: #5f6368;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-input {
  padding: 12px 16px;
  border: 1px solid #dadce0;
  border-radius: 8px;
  font-size: 16px;
  color: #202124;
  background-color: #ffffff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.form-input::placeholder {
  color: #9aa0a6;
}

.form-select {
  padding: 12px 16px;
  border: 1px solid #dadce0;
  border-radius: 8px;
  font-size: 16px;
  color: #202124;
  background-color: #ffffff;
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

/* Search button */
.search-button {
  background-color: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  align-self: flex-start;
}

.search-button:hover {
  background-color: #1557b0;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.2);
}

.search-button:active {
  background-color: #1347a0;
}

/* Trip type selector */
.trip-type-selector {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.trip-type-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.trip-type-radio {
  width: 16px;
  height: 16px;
  border: 2px solid #5f6368;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
}

.trip-type-radio.selected {
  border-color: #1a73e8;
}

.trip-type-radio.selected::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background-color: #1a73e8;
  border-radius: 50%;
}

.trip-type-label {
  font-size: 14px;
  color: #202124;
  cursor: pointer;
}

/* Results section */
.results-section {
  margin-top: 32px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.results-title {
  font-size: 24px;
  font-weight: 400;
  color: #202124;
}

.results-count {
  font-size: 14px;
  color: #5f6368;
}

/* Flight card styles */
.flight-card {
  background-color: #ffffff;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
  transition: box-shadow 0.2s ease, border-color 0.2s ease;
  cursor: pointer;
}

.flight-card:hover {
  box-shadow: 0 2px 12px rgba(32, 33, 36, 0.1);
  border-color: #dadce0;
}

.flight-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.flight-route {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.airport-info {
  text-align: center;
}

.airport-code {
  font-size: 24px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 4px;
}

.airport-name {
  font-size: 12px;
  color: #5f6368;
}

.flight-duration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.duration-time {
  font-size: 14px;
  color: #5f6368;
}

.flight-line {
  width: 100%;
  height: 2px;
  background-color: #e8eaed;
  position: relative;
}

.flight-line::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid #5f6368;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.flight-price {
  text-align: right;
}

.price-amount {
  font-size: 24px;
  font-weight: 500;
  color: #202124;
  margin-bottom: 4px;
}

.price-type {
  font-size: 12px;
  color: #5f6368;
}

.flight-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f1f3f4;
}

.flight-info {
  display: flex;
  gap: 24px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #5f6368;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 14px;
  color: #202124;
}

.select-button {
  background-color: #1a73e8;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.select-button:hover {
  background-color: #1557b0;
}

/* Loading and empty states */
.loading-state {
  text-align: center;
  padding: 48px 24px;
  color: #5f6368;
}

.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #5f6368;
}

.empty-state-title {
  font-size: 20px;
  color: #202124;
  margin-bottom: 8px;
}

.empty-state-message {
  font-size: 14px;
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .main-container {
    padding: 16px;
  }

  .search-section {
    padding: 16px;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .form-group {
    min-width: unset;
  }

  .flight-route {
    flex-direction: column;
    gap: 12px;
  }

  .flight-header {
    flex-direction: column;
    gap: 16px;
  }

  .flight-details {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .flight-info {
    flex-wrap: wrap;
    gap: 16px;
  }
}
