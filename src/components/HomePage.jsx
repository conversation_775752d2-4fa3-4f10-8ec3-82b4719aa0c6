import SearchBar from './SearchBar.jsx';

/**
 * HomePage Component
 * 
 * Main landing page for the flight search application
 * Contains the search interface and welcome content
 */
const HomePage = () => {
  // Handle flight search
  const handleSearch = (searchParams) => {
    console.log("🔍 Search triggered with params:", searchParams);
    
    // Show search details in a nice format
    const { originSkyId, destinationSkyId, date } = searchParams;
    alert(`✈️ Flight Search:\n${originSkyId} → ${destinationSkyId}\nDate: ${date}`);
  };

  return (
    <main className="main-container">
      {/* Search Section */}
      <section className="search-section">
        <h1 className="search-title">Flights</h1>
        <SearchBar onSearch={handleSearch} />
      </section>

      {/* Info Panel */}
      <section className="search-section">
        <div style={{ textAlign: 'center', padding: '24px' }}>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: '500', 
            color: '#1a73e8', 
            marginBottom: '8px' 
          }}>
            🎯 Focus: SearchBar & AirportAutocomplete
          </h2>
          <p style={{ 
            fontSize: '14px', 
            color: '#5f6368', 
            margin: 0,
            lineHeight: '1.5'
          }}>
            This simplified version focuses only on the search functionality.
            Try searching for airports and see the console logs!
          </p>
        </div>
      </section>

      {/* Features Section */}
      <section className="search-section">
        <div style={{ padding: '24px' }}>
          <h3 style={{ 
            fontSize: '18px', 
            fontWeight: '500', 
            color: '#202124', 
            marginBottom: '16px',
            textAlign: 'center'
          }}>
            ✨ What You Can Learn
          </h3>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
            gap: '16px',
            marginTop: '16px'
          }}>
            <div style={{ 
              padding: '16px', 
              backgroundColor: '#f8f9fa', 
              borderRadius: '8px',
              border: '1px solid #e8eaed'
            }}>
              <h4 style={{ 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#1a73e8', 
                marginBottom: '8px',
                margin: '0 0 8px 0'
              }}>
                🔄 Debouncing
              </h4>
              <p style={{ 
                fontSize: '13px', 
                color: '#5f6368', 
                margin: 0,
                lineHeight: '1.4'
              }}>
                Learn how debouncing prevents excessive API calls while typing
              </p>
            </div>

            <div style={{ 
              padding: '16px', 
              backgroundColor: '#f8f9fa', 
              borderRadius: '8px',
              border: '1px solid #e8eaed'
            }}>
              <h4 style={{ 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#1a73e8', 
                marginBottom: '8px',
                margin: '0 0 8px 0'
              }}>
                🎨 Material-UI Autocomplete
              </h4>
              <p style={{ 
                fontSize: '13px', 
                color: '#5f6368', 
                margin: 0,
                lineHeight: '1.4'
              }}>
                Master advanced dropdown components with search functionality
              </p>
            </div>

            <div style={{ 
              padding: '16px', 
              backgroundColor: '#f8f9fa', 
              borderRadius: '8px',
              border: '1px solid #e8eaed'
            }}>
              <h4 style={{ 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#1a73e8', 
                marginBottom: '8px',
                margin: '0 0 8px 0'
              }}>
                ⚛️ React Hooks
              </h4>
              <p style={{ 
                fontSize: '13px', 
                color: '#5f6368', 
                margin: 0,
                lineHeight: '1.4'
              }}>
                Understand useState, useEffect, and useCallback patterns
              </p>
            </div>

            <div style={{ 
              padding: '16px', 
              backgroundColor: '#f8f9fa', 
              borderRadius: '8px',
              border: '1px solid #e8eaed'
            }}>
              <h4 style={{ 
                fontSize: '14px', 
                fontWeight: '500', 
                color: '#1a73e8', 
                marginBottom: '8px',
                margin: '0 0 8px 0'
              }}>
                🌐 API Integration
              </h4>
              <p style={{ 
                fontSize: '13px', 
                color: '#5f6368', 
                margin: 0,
                lineHeight: '1.4'
              }}>
                Learn proper API integration with error handling and loading states
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Instructions Section */}
      <section className="search-section">
        <div style={{ padding: '24px' }}>
          <h3 style={{ 
            fontSize: '18px', 
            fontWeight: '500', 
            color: '#202124', 
            marginBottom: '16px',
            textAlign: 'center'
          }}>
            🚀 How to Use
          </h3>
          
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            gap: '12px',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'flex-start', 
              gap: '12px',
              padding: '12px',
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              border: '1px solid #e8eaed'
            }}>
              <span style={{ 
                backgroundColor: '#1a73e8', 
                color: 'white', 
                borderRadius: '50%', 
                width: '24px', 
                height: '24px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                fontSize: '12px',
                fontWeight: '500',
                flexShrink: 0
              }}>
                1
              </span>
              <div>
                <p style={{ 
                  fontSize: '14px', 
                  color: '#202124', 
                  margin: '0 0 4px 0',
                  fontWeight: '500'
                }}>
                  Select Origin Airport
                </p>
                <p style={{ 
                  fontSize: '13px', 
                  color: '#5f6368', 
                  margin: 0,
                  lineHeight: '1.4'
                }}>
                  Type "new" or "london" in the "From" field and watch the debounced search
                </p>
              </div>
            </div>

            <div style={{ 
              display: 'flex', 
              alignItems: 'flex-start', 
              gap: '12px',
              padding: '12px',
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              border: '1px solid #e8eaed'
            }}>
              <span style={{ 
                backgroundColor: '#1a73e8', 
                color: 'white', 
                borderRadius: '50%', 
                width: '24px', 
                height: '24px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                fontSize: '12px',
                fontWeight: '500',
                flexShrink: 0
              }}>
                2
              </span>
              <div>
                <p style={{ 
                  fontSize: '14px', 
                  color: '#202124', 
                  margin: '0 0 4px 0',
                  fontWeight: '500'
                }}>
                  Select Destination Airport
                </p>
                <p style={{ 
                  fontSize: '13px', 
                  color: '#5f6368', 
                  margin: 0,
                  lineHeight: '1.4'
                }}>
                  Choose your destination and use the swap button to switch airports
                </p>
              </div>
            </div>

            <div style={{ 
              display: 'flex', 
              alignItems: 'flex-start', 
              gap: '12px',
              padding: '12px',
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              border: '1px solid #e8eaed'
            }}>
              <span style={{ 
                backgroundColor: '#1a73e8', 
                color: 'white', 
                borderRadius: '50%', 
                width: '24px', 
                height: '24px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                fontSize: '12px',
                fontWeight: '500',
                flexShrink: 0
              }}>
                3
              </span>
              <div>
                <p style={{ 
                  fontSize: '14px', 
                  color: '#202124', 
                  margin: '0 0 4px 0',
                  fontWeight: '500'
                }}>
                  Pick Date & Search
                </p>
                <p style={{ 
                  fontSize: '13px', 
                  color: '#5f6368', 
                  margin: 0,
                  lineHeight: '1.4'
                }}>
                  Select your travel date and click search to see the console logs
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default HomePage;
