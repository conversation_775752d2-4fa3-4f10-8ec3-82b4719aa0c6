import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  Paper,
  Fade,
} from '@mui/material';
import FlightIcon from '@mui/icons-material/Flight';

const LoadingSpinner = ({ 
  message = 'Loading...', 
  size = 40, 
  fullScreen = false,
  showIcon = true,
  variant = 'default' // 'default', 'search', 'minimal'
}) => {
  const getLoadingContent = () => {
    switch (variant) {
      case 'search':
        return (
          <Paper 
            elevation={3} 
            sx={{ 
              p: 4, 
              textAlign: 'center',
              borderRadius: 2,
              minHeight: 200,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Box sx={{ position: 'relative', mb: 2 }}>
              <CircularProgress size={size} thickness={4} />
              {showIcon && (
                <FlightIcon 
                  sx={{ 
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    fontSize: size * 0.4,
                    color: 'primary.main',
                    animation: 'spin 2s linear infinite',
                    '@keyframes spin': {
                      '0%': {
                        transform: 'translate(-50%, -50%) rotate(0deg)',
                      },
                      '100%': {
                        transform: 'translate(-50%, -50%) rotate(360deg)',
                      },
                    },
                  }} 
                />
              )}
            </Box>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Searching for flights...
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This may take a few moments
            </Typography>
          </Paper>
        );

      case 'minimal':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CircularProgress size={20} thickness={4} />
            <Typography variant="body2" color="text.secondary">
              {message}
            </Typography>
          </Box>
        );

      default:
        return (
          <Box 
            sx={{ 
              display: 'flex', 
              flexDirection: 'column',
              alignItems: 'center',
              gap: 2,
              p: 3,
            }}
          >
            <CircularProgress size={size} thickness={4} />
            <Typography variant="body1" color="text.secondary">
              {message}
            </Typography>
          </Box>
        );
    }
  };

  const content = (
    <Fade in timeout={300}>
      <Box>
        {getLoadingContent()}
      </Box>
    </Fade>
  );

  if (fullScreen) {
    return (
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          zIndex: 9999,
        }}
      >
        {content}
      </Box>
    );
  }

  return content;
};

export default LoadingSpinner;
