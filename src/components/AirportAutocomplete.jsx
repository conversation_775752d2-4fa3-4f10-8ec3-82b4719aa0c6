import React, { useState, useEffect, useCallback } from 'react';
import {
  Autocomplete,
  TextField,
  CircularProgress,
  Box,
  Typography,
} from '@mui/material';
import debounce from 'lodash.debounce';
import FlightTakeoffIcon from '@mui/icons-material/FlightTakeoff';
import FlightLandIcon from '@mui/icons-material/FlightLand';
import { fetchAirports } from '../api/fetchAirports.js';
import { SEARCH_DEBOUNCE_DELAY } from '../utils/constants.js';

const AirportAutocomplete = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  helperText,
  type = 'origin', // 'origin' or 'destination'
  disabled = false,
  required = false,
}) => {
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchQuery) => {
      if (!searchQuery || searchQuery.length < 2) {
        setOptions([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const airports = await fetchAirports(searchQuery);
        setOptions(airports);
      } catch (error) {
        console.error('Error searching airports:', error);
        setOptions([]);
      } finally {
        setLoading(false);
      }
    }, SEARCH_DEBOUNCE_DELAY),
    []
  );

  // Handle input change
  const handleInputChange = (event, newInputValue) => {
    setInputValue(newInputValue);
    debouncedSearch(newInputValue);
  };

  // Handle selection change
  const handleChange = (event, newValue) => {
    onChange(newValue);
  };

  // Handle Enter key press - select first option if available
  const handleKeyDown = (event) => {
    if (event.key === 'Enter' && options.length > 0 && !value) {
      event.preventDefault();
      onChange(options[0]);
    }
  };

  // Clean up debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // Get appropriate icon based on type
  const getIcon = () => {
    return type === 'origin' ? (
      <FlightTakeoffIcon color="action" />
    ) : (
      <FlightLandIcon color="action" />
    );
  };

  return (
    <Autocomplete
      value={value}
      onChange={handleChange}
      inputValue={inputValue}
      onInputChange={handleInputChange}
      options={options}
      loading={loading}
      disabled={disabled}
      getOptionLabel={(option) => option?.suggestionTitle || option?.name || ''}
      isOptionEqualToValue={(option, value) => option?.skyId === value?.skyId}
      filterOptions={(x) => x} // Disable built-in filtering since we use API
      noOptionsText={
        inputValue.length < 2
          ? 'Type at least 2 characters to search'
          : loading
          ? 'Searching...'
          : 'No airports found'
      }
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          error={error}
          helperText={helperText}
          required={required}
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                {getIcon()}
              </Box>
            ),
            endAdornment: (
              <>
                {loading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
          onKeyDown={handleKeyDown}
        />
      )}
      renderOption={(props, option) => (
        <Box component="li" {...props}>
          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
            <Typography variant="body1" component="div">
              {option.suggestionTitle || option.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {option.subtitle}
            </Typography>
          </Box>
        </Box>
      )}
      sx={{
        minWidth: 250,
        '& .MuiOutlinedInput-root': {
          paddingLeft: 1,
        },
      }}
    />
  );
};

export default AirportAutocomplete;
