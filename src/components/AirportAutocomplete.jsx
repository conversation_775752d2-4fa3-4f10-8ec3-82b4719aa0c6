import React, { useState, useEffect, useCallback } from 'react';
import {
  Autocomplete,
  TextField,
  CircularProgress,
  Box,
  Typography,
} from '@mui/material';
import debounce from 'lodash.debounce';
import FlightTakeoffIcon from '@mui/icons-material/FlightTakeoff';
import FlightLandIcon from '@mui/icons-material/FlightLand';
import { fetchAirports } from '../api/fetchAirports.js';
import { SEARCH_DEBOUNCE_DELAY } from '../utils/constants.js';

/**
 * AirportAutocomplete Component
 *
 * This component provides an autocomplete search for airports using Material-UI's Autocomplete
 * with debounced API calls to prevent excessive requests while the user is typing.
 *
 * Key Features:
 * 1. Debounced search - waits 300ms after user stops typing before making API call
 * 2. Loading states - shows spinner while searching
 * 3. Error handling - gracefully handles API errors
 * 4. Keyboard navigation - Enter key selects first option
 * 5. Responsive design - works on all screen sizes
 */
const AirportAutocomplete = ({
  label,           // Label text for the input field
  placeholder,     // Placeholder text
  value,          // Currently selected airport object
  onChange,       // Callback when user selects an airport
  error,          // <PERSON><PERSON>an indicating if there's an error
  helperText,     // Error message or help text
  type = 'origin', // 'origin' or 'destination' - affects the icon shown
  disabled = false,
  required = false,
}) => {
  // STATE MANAGEMENT
  // ===============

  // Array of airport options returned from API
  const [options, setOptions] = useState([]);

  // Loading state for showing spinner during API calls
  const [loading, setLoading] = useState(false);

  // Current text in the input field (what user is typing)
  const [inputValue, setInputValue] = useState('');

  // DEBOUNCED SEARCH FUNCTION
  // ========================

  /**
   * Why use debouncing?
   * - Without debouncing: User types "New York" → makes 8 API calls (N, Ne, New, New , New Y, New Yo, New Yor, New York)
   * - With debouncing: User types "New York" → waits 300ms after user stops typing → makes 1 API call
   *
   * This saves API quota, reduces server load, and improves user experience
   */
  const debouncedSearch = useCallback(
    debounce(async (searchQuery) => {
      // Don't search if query is too short
      if (!searchQuery || searchQuery.length < 2) {
        setOptions([]);
        setLoading(false);
        return;
      }

      console.log(`🔍 Making API call for: "${searchQuery}"`);

      try {
        // Show loading spinner
        setLoading(true);

        // Make API call to fetch airports
        const airports = await fetchAirports(searchQuery);
        console.log('✅ Airports received:', airports);

        // Update options list with results
        setOptions(airports);
      } catch (error) {
        console.error('❌ Error searching airports:', error);
        // Clear options on error to prevent showing stale data
        setOptions([]);
      } finally {
        // Always hide loading spinner
        setLoading(false);
      }
    }, SEARCH_DEBOUNCE_DELAY), // 300ms delay
    [] // Empty dependency array - function is created once and reused
  );

  // EVENT HANDLERS
  // ==============

  /**
   * Handle input text changes (when user types)
   * This is called on every keystroke
   */
  const handleInputChange = (event, newInputValue) => {
    console.log(`📝 User typed: "${newInputValue}"`);

    // Update the input field immediately (for responsive UI)
    setInputValue(newInputValue);

    // Trigger debounced search (will wait 300ms before actually searching)
    debouncedSearch(newInputValue);
  };

  /**
   * Handle selection changes (when user picks an airport from dropdown)
   */
  const handleChange = (event, newValue) => {
    console.log('✈️ Airport selected:', newValue);

    // Call parent component's onChange with selected airport
    onChange(newValue);
  };

  /**
   * Handle Enter key press - auto-select first option for better UX
   */
  const handleKeyDown = (event) => {
    if (event.key === 'Enter' && options.length > 0 && !value) {
      event.preventDefault();
      console.log('⌨️ Enter pressed - selecting first option:', options[0]);
      onChange(options[0]);
    }
  };

  // CLEANUP EFFECT
  // ==============

  /**
   * Clean up debounced function when component unmounts
   * This prevents memory leaks and cancelled API calls
   */
  useEffect(() => {
    return () => {
      // Cancel any pending debounced calls
      debouncedSearch.cancel();
      console.log('🧹 Cleanup: Cancelled pending searches');
    };
  }, [debouncedSearch]);

  // HELPER FUNCTIONS
  // ================

  /**
   * Get appropriate icon based on component type
   * Origin = takeoff icon, Destination = landing icon
   */
  const getIcon = () => {
    return type === 'origin' ? (
      <FlightTakeoffIcon color="action" />
    ) : (
      <FlightLandIcon color="action" />
    );
  };

  // RENDER COMPONENT
  // ================

  return (
    <Autocomplete
      // CONTROLLED COMPONENT PROPS
      // =========================
      value={value}                    // Currently selected airport object
      onChange={handleChange}          // Called when user selects an option
      inputValue={inputValue}          // Current text in input field
      onInputChange={handleInputChange} // Called when user types

      // DATA PROPS
      // ==========
      options={options}                // Array of airport options from API
      loading={loading}                // Shows loading spinner when true
      disabled={disabled}              // Disables the entire component

      // OPTION CONFIGURATION
      // ===================
      getOptionLabel={(option) => option?.suggestionTitle || option?.name || ''}
      isOptionEqualToValue={(option, value) => option?.skyId === value?.skyId}
      filterOptions={(x) => x}         // Disable built-in filtering (we use API filtering)

      // NO OPTIONS MESSAGE
      // ==================
      noOptionsText={
        inputValue.length < 2
          ? 'Type at least 2 characters to search'  // User hasn't typed enough
          : loading
          ? 'Searching...'                          // API call in progress
          : 'No airports found'                     // No results from API
      }

      // INPUT FIELD RENDERER
      // ===================
      renderInput={(params) => (
        <TextField
          {...params}                  // Spread MUI Autocomplete's default props
          label={label}
          placeholder={placeholder}
          error={error}
          helperText={helperText}
          required={required}
          slotProps={{
            input: {
              ...params.InputProps,
              // START ICON (takeoff/landing)
              startAdornment: (
                <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                  {getIcon()}
                </Box>
              ),
              // END ICON (loading spinner + dropdown arrow)
              endAdornment: (
                <>
                  {loading ? <CircularProgress color="inherit" size={20} /> : null}
                  {params.InputProps.endAdornment}
                </>
              ),
            },
          }}
          onKeyDown={handleKeyDown}     // Handle Enter key for auto-selection
        />
      )}

      // DROPDOWN OPTION RENDERER
      // ========================
      renderOption={(props, option) => (
        <Box component="li" {...props}>
          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
            {/* Main airport name */}
            <Typography variant="body1" component="div">
              {option.suggestionTitle || option.name}
            </Typography>
            {/* Country/region subtitle */}
            <Typography variant="body2" color="text.secondary">
              {option.subtitle}
            </Typography>
          </Box>
        </Box>
      )}

      // STYLING
      // =======
      sx={{
        minWidth: 250,
        '& .MuiOutlinedInput-root': {
          paddingLeft: 1,              // Space for the start icon
        },
      }}
    />
  );
};

export default AirportAutocomplete;
