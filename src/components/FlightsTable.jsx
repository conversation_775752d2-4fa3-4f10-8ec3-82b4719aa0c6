import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Box,
  Typography,
  Avatar,
  Chip,
  Button,
  Divider,
  useTheme,
  useMediaQuery,
  Card,
  CardContent,
  Grid,
} from '@mui/material';
import {
  FlightTakeoff as TakeoffIcon,
  FlightLand as LandIcon,
  AccessTime as TimeIcon,
  AttachMoney as PriceIcon,
} from '@mui/icons-material';
import { formatDuration, formatStops, formatPrice } from '../utils/formatDuration.js';
import { formatTimeForDisplay } from '../utils/formatDate.js';
import { DEFAULT_ROWS_PER_PAGE, ROWS_PER_PAGE_OPTIONS } from '../utils/constants.js';

const FlightsTable = ({ 
  flights = [], 
  loading = false, 
  totalResults = 0,
  onSelectFlight 
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(DEFAULT_ROWS_PER_PAGE);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSelectFlight = (flight) => {
    if (onSelectFlight) {
      onSelectFlight(flight);
    }
  };

  // Get airline logo URL with fallback
  const getAirlineLogo = (carriers) => {
    if (!carriers || carriers.length === 0) return null;
    const carrier = carriers[0];
    return carrier.logoUrl || null;
  };

  // Get airline name
  const getAirlineName = (carriers) => {
    if (!carriers || carriers.length === 0) return 'Unknown Airline';
    return carriers[0].name || 'Unknown Airline';
  };

  // Get airline code
  const getAirlineCode = (carriers) => {
    if (!carriers || carriers.length === 0) return '';
    return carriers[0].alternateId || carriers[0].displayCode || '';
  };

  // Render mobile card view
  const renderMobileCard = (flight, index) => (
    <Card key={flight.id} sx={{ mb: 2, borderRadius: 2 }}>
      <CardContent sx={{ p: 2 }}>
        {/* Airline and Price Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getAirlineLogo(flight.carriers) && (
              <Avatar
                src={getAirlineLogo(flight.carriers)}
                alt={getAirlineName(flight.carriers)}
                sx={{ width: 32, height: 32 }}
              />
            )}
            <Box>
              <Typography variant="subtitle2" fontWeight="bold">
                {getAirlineName(flight.carriers)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {getAirlineCode(flight.carriers)}
              </Typography>
            </Box>
          </Box>
          <Typography variant="h6" color="primary" fontWeight="bold">
            {flight.price.formatted}
          </Typography>
        </Box>

        {/* Route and Time */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={5}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold">
                {formatTimeForDisplay(flight.departure)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {flight.origin.code}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {flight.origin.city}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={2}>
            <Box sx={{ textAlign: 'center', py: 1 }}>
              <TakeoffIcon color="action" />
              <Typography variant="caption" display="block" color="text.secondary">
                {formatDuration(flight.duration)}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={5}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold">
                {formatTimeForDisplay(flight.arrival)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {flight.destination.code}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {flight.destination.city}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Stops and Select Button */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Chip
            label={formatStops(flight.stops)}
            size="small"
            color={flight.stops === 0 ? 'success' : 'default'}
            variant="outlined"
          />
          <Button
            variant="contained"
            size="small"
            onClick={() => handleSelectFlight(flight)}
            sx={{ borderRadius: 2 }}
          >
            Select
          </Button>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography>Loading flights...</Typography>
      </Paper>
    );
  }

  if (!flights || flights.length === 0) {
    return (
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No flights found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Try adjusting your search criteria
        </Typography>
      </Paper>
    );
  }

  // Mobile view
  if (isMobile) {
    const startIndex = page * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    const paginatedFlights = flights.slice(startIndex, endIndex);

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          {totalResults > 0 ? `${totalResults} flights found` : `${flights.length} flights found`}
        </Typography>

        {paginatedFlights.map((flight, index) => renderMobileCard(flight, index))}

        <TablePagination
          component="div"
          count={flights.length}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS}
        />
      </Box>
    );
  }

  // Desktop table view
  const startIndex = page * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedFlights = flights.slice(startIndex, endIndex);

  return (
    <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
      <Box sx={{ p: 2, backgroundColor: 'grey.50' }}>
        <Typography variant="h6">
          {totalResults > 0 ? `${totalResults} flights found` : `${flights.length} flights found`}
        </Typography>
      </Box>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'grey.100' }}>
              <TableCell>Airline</TableCell>
              <TableCell>Route</TableCell>
              <TableCell>Departure</TableCell>
              <TableCell>Arrival</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Stops</TableCell>
              <TableCell align="right">Price</TableCell>
              <TableCell align="center">Action</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedFlights.map((flight, index) => (
              <TableRow
                key={flight.id}
                hover
                sx={{
                  '&:hover': { backgroundColor: 'grey.50' },
                  borderBottom: '1px solid',
                  borderColor: 'divider',
                }}
              >
                {/* Airline */}
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getAirlineLogo(flight.carriers) && (
                      <Avatar
                        src={getAirlineLogo(flight.carriers)}
                        alt={getAirlineName(flight.carriers)}
                        sx={{ width: 32, height: 32 }}
                      />
                    )}
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {getAirlineName(flight.carriers)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {getAirlineCode(flight.carriers)}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>

                {/* Route */}
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="body2" fontWeight="bold">
                        {flight.origin.code}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {flight.origin.city}
                      </Typography>
                    </Box>
                    <TakeoffIcon color="action" sx={{ mx: 1 }} />
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="body2" fontWeight="bold">
                        {flight.destination.code}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {flight.destination.city}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>

                {/* Departure */}
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {formatTimeForDisplay(flight.departure)}
                  </Typography>
                </TableCell>

                {/* Arrival */}
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {formatTimeForDisplay(flight.arrival)}
                  </Typography>
                </TableCell>

                {/* Duration */}
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <TimeIcon fontSize="small" color="action" />
                    <Typography variant="body2">
                      {formatDuration(flight.duration)}
                    </Typography>
                  </Box>
                </TableCell>

                {/* Stops */}
                <TableCell>
                  <Chip
                    label={formatStops(flight.stops)}
                    size="small"
                    color={flight.stops === 0 ? 'success' : 'default'}
                    variant="outlined"
                  />
                </TableCell>

                {/* Price */}
                <TableCell align="right">
                  <Typography variant="h6" color="primary" fontWeight="bold">
                    {flight.price.formatted}
                  </Typography>
                </TableCell>

                {/* Action */}
                <TableCell align="center">
                  <Button
                    variant="contained"
                    size="small"
                    onClick={() => handleSelectFlight(flight)}
                    sx={{ borderRadius: 2 }}
                  >
                    Select
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        component="div"
        count={flights.length}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS}
        sx={{ borderTop: '1px solid', borderColor: 'divider' }}
      />
    </Paper>
  );
};

export default FlightsTable;
