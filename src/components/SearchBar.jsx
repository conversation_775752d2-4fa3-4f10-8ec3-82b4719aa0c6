import React, { useState } from 'react';
import {
  Box,
  Paper,
  Grid,
  Button,
  TextField,
  Typography,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  SwapHoriz as SwapIcon,
} from '@mui/icons-material';
import AirportAutocomplete from './AirportAutocomplete.jsx';
import { formatDateForAPI, getTodayDate } from '../utils/formatDate.js';

/**
 * Simplified SearchBar Component
 *
 * Focus: Airport selection and basic search functionality
 * Clean, minimal design without overwhelming features
 */
const SearchBar = ({ onSearch }) => {
  // Simple state - just the essentials
  const [searchParams, setSearchParams] = useState({
    origin: null,
    destination: null,
    departureDate: formatDateForAPI(getTodayDate()),
  });

  const [errors, setErrors] = useState({});

  // Handle input changes - simplified
  const handleInputChange = (field, value) => {
    console.log(`📝 SearchBar: ${field} changed to:`, value);

    setSearchParams(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear any existing error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  // Swap origin and destination airports
  const handleSwapAirports = () => {
    console.log('🔄 Swapping airports');
    setSearchParams(prev => ({
      ...prev,
      origin: prev.destination,
      destination: prev.origin,
    }));
  };

  // Simple validation - just check required fields
  const validateForm = () => {
    const newErrors = {};

    if (!searchParams.origin) {
      newErrors.origin = 'Please select origin airport';
    }

    if (!searchParams.destination) {
      newErrors.destination = 'Please select destination airport';
    }

    if (!searchParams.departureDate) {
      newErrors.departureDate = 'Please select departure date';
    }

    // Check if origin and destination are the same
    if (searchParams.origin && searchParams.destination &&
        searchParams.origin.skyId === searchParams.destination.skyId) {
      newErrors.destination = 'Destination must be different from origin';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle search - simplified
  const handleSearch = () => {
    console.log('🔍 Search button clicked');

    if (!validateForm()) {
      console.log('❌ Validation failed:', errors);
      return;
    }

    // Create simple search data
    const searchData = {
      originSkyId: searchParams.origin.skyId,
      destinationSkyId: searchParams.destination.skyId,
      originEntityId: searchParams.origin.entityId,
      destinationEntityId: searchParams.destination.entityId,
      date: searchParams.departureDate,
    };

    console.log('✅ Search data:', searchData);
    onSearch(searchData);
  };

  return (
    <Paper
      elevation={3}
      sx={{
        p: 4,
        borderRadius: 3,
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
      }}
    >
      {/* Header */}
      <Typography variant="h4" component="h2" gutterBottom align="center" sx={{ mb: 4 }}>
        ✈️ Search Flights
      </Typography>

      {/* Airport Selection Row */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={3} alignItems="center">
          {/* Origin Airport */}
          <Grid item xs={12} md={5}>
            <AirportAutocomplete
              label="From"
              placeholder="Select origin airport"
              value={searchParams.origin}
              onChange={(value) => handleInputChange('origin', value)}
              error={!!errors.origin}
              helperText={errors.origin}
              type="origin"
              required
            />
          </Grid>

          {/* Swap Button */}
          <Grid item xs={12} md={2} sx={{ textAlign: 'center' }}>
            <Tooltip title="Swap airports">
              <IconButton
                onClick={handleSwapAirports}
                sx={{
                  color: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  },
                  p: 2,
                }}
              >
                <SwapIcon />
              </IconButton>
            </Tooltip>
          </Grid>

          {/* Destination Airport */}
          <Grid item xs={12} md={5}>
            <AirportAutocomplete
              label="To"
              placeholder="Select destination airport"
              value={searchParams.destination}
              onChange={(value) => handleInputChange('destination', value)}
              error={!!errors.destination}
              helperText={errors.destination}
              type="destination"
              required
            />
          </Grid>
        </Grid>
      </Box>

      {/* Date and Search Row */}
      <Grid container spacing={3} alignItems="center">
        {/* Departure Date */}
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            type="date"
            label="Departure Date"
            value={searchParams.departureDate}
            onChange={(e) => handleInputChange('departureDate', e.target.value)}
            error={!!errors.departureDate}
            helperText={errors.departureDate}
            slotProps={{
              inputLabel: { shrink: true },
            }}
            required
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                '& fieldset': {
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                },
              },
              '& .MuiInputLabel-root': {
                color: 'rgba(255, 255, 255, 0.8)',
              },
              '& .MuiInputBase-input': {
                color: 'white',
              },
            }}
          />
        </Grid>

        {/* Search Button */}
        <Grid item xs={12} md={6}>
          <Button
            fullWidth
            variant="contained"
            size="large"
            onClick={handleSearch}
            startIcon={<SearchIcon />}
            sx={{
              height: '56px',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              color: '#667eea',
              fontWeight: 'bold',
              fontSize: '1.1rem',
              '&:hover': {
                backgroundColor: 'white',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.2s ease',
            }}
          >
            Search Flights
          </Button>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default SearchBar;
