import React, { useState } from 'react';
import {
  Box,
  Paper,
  Grid,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  SwapHoriz as SwapIcon,
  DateRange as DateIcon,
} from '@mui/icons-material';
import AirportAutocomplete from './AirportAutocomplete.jsx';
import { formatDateForAPI, getTodayDate, getMinDate, getMaxDate } from '../utils/formatDate.js';
import { CABIN_CLASSES, DEFAULT_SEARCH_PARAMS } from '../utils/constants.js';

const SearchBar = ({ onSearch, loading = false }) => {
  const [searchParams, setSearchParams] = useState({
    origin: null,
    destination: null,
    departureDate: formatDateForAPI(getTodayDate()),
    returnDate: '',
    cabinClass: DEFAULT_SEARCH_PARAMS.cabinClass,
    adults: DEFAULT_SEARCH_PARAMS.adults,
    children: DEFAULT_SEARCH_PARAMS.childrens,
    infants: DEFAULT_SEARCH_PARAMS.infants,
    tripType: 'one-way', // 'one-way' or 'round-trip'
  });

  const [errors, setErrors] = useState({});

  // Handle input changes
  const handleInputChange = (field, value) => {
    setSearchParams(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  // Swap origin and destination
  const handleSwapAirports = () => {
    setSearchParams(prev => ({
      ...prev,
      origin: prev.destination,
      destination: prev.origin,
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!searchParams.origin) {
      newErrors.origin = 'Please select origin airport';
    }

    if (!searchParams.destination) {
      newErrors.destination = 'Please select destination airport';
    }

    if (!searchParams.departureDate) {
      newErrors.departureDate = 'Please select departure date';
    }

    if (searchParams.tripType === 'round-trip' && !searchParams.returnDate) {
      newErrors.returnDate = 'Please select return date';
    }

    if (searchParams.origin && searchParams.destination && 
        searchParams.origin.skyId === searchParams.destination.skyId) {
      newErrors.destination = 'Destination must be different from origin';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle search
  const handleSearch = () => {
    if (!validateForm()) {
      return;
    }

    const searchData = {
      originSkyId: searchParams.origin.skyId,
      destinationSkyId: searchParams.destination.skyId,
      originEntityId: searchParams.origin.entityId,
      destinationEntityId: searchParams.destination.entityId,
      date: searchParams.departureDate,
      returnDate: searchParams.tripType === 'round-trip' ? searchParams.returnDate : undefined,
      cabinClass: searchParams.cabinClass,
      adults: searchParams.adults,
      childrens: searchParams.children,
      infants: searchParams.infants,
    };

    onSearch(searchData);
  };

  return (
    <Paper 
      elevation={3} 
      sx={{ 
        p: 3, 
        mb: 3,
        borderRadius: 2,
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
      }}
    >
      <Typography variant="h4" component="h1" gutterBottom align="center" sx={{ mb: 3 }}>
        ✈️ Find Your Perfect Flight
      </Typography>

      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          {/* Origin Airport */}
          <Grid item xs={12} md={5}>
            <AirportAutocomplete
              label="From"
              placeholder="Origin airport"
              value={searchParams.origin}
              onChange={(value) => handleInputChange('origin', value)}
              error={!!errors.origin}
              helperText={errors.origin}
              type="origin"
              required
            />
          </Grid>

          {/* Swap Button */}
          <Grid item xs={12} md={2} sx={{ textAlign: 'center' }}>
            <Tooltip title="Swap airports">
              <IconButton 
                onClick={handleSwapAirports}
                sx={{ 
                  color: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  },
                }}
              >
                <SwapIcon />
              </IconButton>
            </Tooltip>
          </Grid>

          {/* Destination Airport */}
          <Grid item xs={12} md={5}>
            <AirportAutocomplete
              label="To"
              placeholder="Destination airport"
              value={searchParams.destination}
              onChange={(value) => handleInputChange('destination', value)}
              error={!!errors.destination}
              helperText={errors.destination}
              type="destination"
              required
            />
          </Grid>
        </Grid>
      </Box>

      <Grid container spacing={2} sx={{ mb: 3 }}>
        {/* Departure Date */}
        <Grid item xs={12} sm={6} md={3}>
          <TextField
            fullWidth
            type="date"
            label="Departure"
            value={searchParams.departureDate}
            onChange={(e) => handleInputChange('departureDate', e.target.value)}
            error={!!errors.departureDate}
            helperText={errors.departureDate}
            InputLabelProps={{ shrink: true }}
            inputProps={{
              min: formatDateForAPI(getMinDate()),
              max: formatDateForAPI(getMaxDate()),
            }}
            required
          />
        </Grid>

        {/* Return Date */}
        <Grid item xs={12} sm={6} md={3}>
          <TextField
            fullWidth
            type="date"
            label="Return (Optional)"
            value={searchParams.returnDate}
            onChange={(e) => handleInputChange('returnDate', e.target.value)}
            error={!!errors.returnDate}
            helperText={errors.returnDate}
            InputLabelProps={{ shrink: true }}
            inputProps={{
              min: searchParams.departureDate || formatDateForAPI(getMinDate()),
              max: formatDateForAPI(getMaxDate()),
            }}
          />
        </Grid>

        {/* Cabin Class */}
        <Grid item xs={12} sm={6} md={2}>
          <FormControl fullWidth>
            <InputLabel>Class</InputLabel>
            <Select
              value={searchParams.cabinClass}
              onChange={(e) => handleInputChange('cabinClass', e.target.value)}
              label="Class"
            >
              {CABIN_CLASSES.map((cabin) => (
                <MenuItem key={cabin.value} value={cabin.value}>
                  {cabin.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Passengers */}
        <Grid item xs={12} sm={6} md={2}>
          <TextField
            fullWidth
            type="number"
            label="Adults"
            value={searchParams.adults}
            onChange={(e) => handleInputChange('adults', Math.max(1, parseInt(e.target.value) || 1))}
            inputProps={{ min: 1, max: 9 }}
          />
        </Grid>

        {/* Search Button */}
        <Grid item xs={12} md={2}>
          <Button
            fullWidth
            variant="contained"
            size="large"
            onClick={handleSearch}
            disabled={loading}
            startIcon={loading ? null : <SearchIcon />}
            sx={{
              height: '56px',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              color: '#667eea',
              fontWeight: 'bold',
              '&:hover': {
                backgroundColor: 'white',
              },
              '&:disabled': {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
              },
            }}
          >
            {loading ? 'Searching...' : 'Search Flights'}
          </Button>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default SearchBar;
