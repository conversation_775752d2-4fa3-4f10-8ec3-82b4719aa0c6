import { useState } from 'react';
import AirportAutocomplete from './AirportAutocomplete.jsx';
import { formatDateForAPI, getTodayDate } from '../utils/formatDate.js';

/**
 * Google Flights-inspired SearchBar Component
 * Clean, white design matching Google's interface
 */
const SearchBar = ({ onSearch }) => {
  // Simple state - just the essentials
  const [searchParams, setSearchParams] = useState({
    origin: null,
    destination: null,
    departureDate: formatDateForAPI(getTodayDate()),
    tripType: 'round-trip', // round-trip or one-way
  });

  const [errors, setErrors] = useState({});

  // Handle input changes - simplified
  const handleInputChange = (field, value) => {
    console.log(`📝 SearchBar: ${field} changed to:`, value);

    setSearchParams(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear any existing error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  // Swap origin and destination airports
  const handleSwapAirports = () => {
    console.log('🔄 Swapping airports');
    setSearchParams(prev => ({
      ...prev,
      origin: prev.destination,
      destination: prev.origin,
    }));
  };

  // Simple validation - just check required fields
  const validateForm = () => {
    const newErrors = {};

    if (!searchParams.origin) {
      newErrors.origin = 'Please select origin airport';
    }

    if (!searchParams.destination) {
      newErrors.destination = 'Please select destination airport';
    }

    if (!searchParams.departureDate) {
      newErrors.departureDate = 'Please select departure date';
    }

    // Check if origin and destination are the same
    if (searchParams.origin && searchParams.destination &&
        searchParams.origin.skyId === searchParams.destination.skyId) {
      newErrors.destination = 'Destination must be different from origin';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle search - simplified
  const handleSearch = () => {
    console.log('🔍 Search button clicked');

    if (!validateForm()) {
      console.log('❌ Validation failed:', errors);
      return;
    }

    // Create simple search data
    const searchData = {
      originSkyId: searchParams.origin.skyId,
      destinationSkyId: searchParams.destination.skyId,
      originEntityId: searchParams.origin.entityId,
      destinationEntityId: searchParams.destination.entityId,
      date: searchParams.departureDate,
    };

    console.log('✅ Search data:', searchData);
    onSearch(searchData);
  };

  return (
    <div className="search-form">
      {/* Trip Type Selector */}
      <div className="trip-type-selector">
        <div className="trip-type-option">
          <div
            className={`trip-type-radio ${searchParams.tripType === 'round-trip' ? 'selected' : ''}`}
            onClick={() => handleInputChange('tripType', 'round-trip')}
          />
          <span
            className="trip-type-label"
            onClick={() => handleInputChange('tripType', 'round-trip')}
          >
            Round trip
          </span>
        </div>
        <div className="trip-type-option">
          <div
            className={`trip-type-radio ${searchParams.tripType === 'one-way' ? 'selected' : ''}`}
            onClick={() => handleInputChange('tripType', 'one-way')}
          />
          <span
            className="trip-type-label"
            onClick={() => handleInputChange('tripType', 'one-way')}
          >
            One way
          </span>
        </div>
      </div>

      {/* Airport Selection Row */}
      <div className="form-row">
        {/* Origin Airport */}
        <div className="form-group">
          <label className="form-label">From</label>
          <AirportAutocomplete
            placeholder="Where from?"
            value={searchParams.origin}
            onChange={(value) => handleInputChange('origin', value)}
            error={!!errors.origin}
            helperText={errors.origin}
            type="origin"
            required
          />
        </div>

        {/* Swap Button */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '20px 8px 0 8px'
        }}>
          <button
            type="button"
            onClick={handleSwapAirports}
            style={{
              background: 'none',
              border: '1px solid #dadce0',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = '#f8f9fa';
              e.target.style.borderColor = '#1a73e8';
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.borderColor = '#dadce0';
            }}
          >
            ⇄
          </button>
        </div>

        {/* Destination Airport */}
        <div className="form-group">
          <label className="form-label">To</label>
          <AirportAutocomplete
            placeholder="Where to?"
            value={searchParams.destination}
            onChange={(value) => handleInputChange('destination', value)}
            error={!!errors.destination}
            helperText={errors.destination}
            type="destination"
            required
          />
        </div>
      </div>

      {/* Date and Search Row */}
      <div className="form-row">
        {/* Departure Date */}
        <div className="form-group">
          <label className="form-label">Departure</label>
          <input
            type="date"
            className="form-input"
            value={searchParams.departureDate}
            onChange={(e) => handleInputChange('departureDate', e.target.value)}
            required
          />
          {errors.departureDate && (
            <span style={{ fontSize: '12px', color: '#d93025', marginTop: '4px' }}>
              {errors.departureDate}
            </span>
          )}
        </div>

        {/* Return Date (if round trip) */}
        {searchParams.tripType === 'round-trip' && (
          <div className="form-group">
            <label className="form-label">Return</label>
            <input
              type="date"
              className="form-input"
              value={searchParams.returnDate || ''}
              onChange={(e) => handleInputChange('returnDate', e.target.value)}
              min={searchParams.departureDate}
            />
          </div>
        )}

        {/* Search Button */}
        <div className="form-group" style={{ alignSelf: 'flex-end' }}>
          <button
            type="button"
            className="search-button"
            onClick={handleSearch}
          >
            🔍 Search
          </button>
        </div>
      </div>
    </div>
  );
};

export default SearchBar;
