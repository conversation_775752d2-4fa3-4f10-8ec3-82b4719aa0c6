import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Box,
  Button,
  TextField,
  Alert,
  Divider,
} from '@mui/material';
import { fetchAirports } from '../api/fetchAirports.js';

/**
 * Debug Panel Component
 * 
 * This component helps you understand:
 * 1. How API calls work
 * 2. What data is returned
 * 3. How to debug network issues
 * 4. Error handling patterns
 */
const DebugPanel = () => {
  const [query, setQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  const testApiCall = async () => {
    if (!query.trim()) {
      setError('Please enter a search query');
      return;
    }

    console.log('🧪 DEBUG: Starting API test...');
    console.log('🧪 DEBUG: Query:', query);
    console.log('🧪 DEBUG: API Key:', import.meta.env.VITE_RAPIDAPI_KEY ? 'Present' : 'Missing');
    
    try {
      setLoading(true);
      setError('');
      setResult(null);
      
      console.log('🧪 DEBUG: Making API call...');
      const airports = await fetchAirports(query);
      
      console.log('🧪 DEBUG: API call successful!');
      console.log('🧪 DEBUG: Result:', airports);
      
      setResult(airports);
    } catch (err) {
      console.error('🧪 DEBUG: API call failed:', err);
      setError(err.message || 'API call failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper sx={{ p: 3, mb: 3, backgroundColor: '#f8f9fa' }}>
      <Typography variant="h6" gutterBottom color="primary">
        🧪 API Debug Panel
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Use this panel to test the airport search API directly and understand how it works.
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 2, alignItems: 'flex-start' }}>
        <TextField
          label="Search Query"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="e.g., New York, London, Tokyo"
          size="small"
          sx={{ flexGrow: 1 }}
        />
        <Button
          variant="contained"
          onClick={testApiCall}
          disabled={loading}
          size="small"
        >
          {loading ? 'Testing...' : 'Test API'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          <strong>Error:</strong> {error}
          <br />
          <Typography variant="caption">
            💡 Check the Network tab in DevTools to see the actual HTTP request
          </Typography>
        </Alert>
      )}

      {result && (
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            ✅ API Response ({result.length} airports found):
          </Typography>
          <Box
            sx={{
              backgroundColor: 'white',
              p: 2,
              borderRadius: 1,
              border: '1px solid #e0e0e0',
              maxHeight: 300,
              overflow: 'auto',
            }}
          >
            <pre style={{ margin: 0, fontSize: '12px' }}>
              {JSON.stringify(result, null, 2)}
            </pre>
          </Box>
        </Box>
      )}

      <Divider sx={{ my: 2 }} />

      <Typography variant="subtitle2" gutterBottom>
        🔍 How to Debug Network Issues:
      </Typography>
      <Typography variant="body2" component="div" sx={{ pl: 2 }}>
        1. Open Browser DevTools (F12)<br />
        2. Go to <strong>Network</strong> tab<br />
        3. Clear existing requests (🚫 icon)<br />
        4. Click "Test API" button above<br />
        5. Look for request to <code>sky-scrapper.p.rapidapi.com</code><br />
        6. Check the request headers and response
      </Typography>
    </Paper>
  );
};

export default DebugPanel;
