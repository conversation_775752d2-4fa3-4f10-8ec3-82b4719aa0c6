import { useLocation, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import SearchBar from '../components/SearchBar.jsx';

/**
 * SearchPage Component
 * 
 * Displays search results page with SearchBar at the top
 * Shows search parameters and will display flight results in the future
 */
const SearchPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useState(null);

  // Get search parameters from navigation state or URL
  useEffect(() => {
    if (location.state?.searchParams) {
      setSearchParams(location.state.searchParams);
    } else {
      // If no search params, redirect to home
      navigate('/', { replace: true });
    }
  }, [location.state, navigate]);

  // Handle new search from SearchBar
  const handleSearch = (newSearchParams) => {
    console.log("🔍 New search from SearchPage:", newSearchParams);
    setSearchParams(newSearchParams);
    
    // Update URL with new search params (optional)
    navigate('/search', { 
      state: { searchParams: newSearchParams },
      replace: true 
    });
  };

  if (!searchParams) {
    return (
      <main className="main-container">
        <div style={{ textAlign: 'center', padding: '48px 24px' }}>
          <p style={{ fontSize: '16px', color: '#5f6368' }}>
            Loading search...
          </p>
        </div>
      </main>
    );
  }

  return (
    <main className="main-container">
      {/* Search Section */}
      <section className="search-section">
        <h1 className="search-title">Search Results</h1>
        <SearchBar 
          onSearch={handleSearch}
          initialValues={{
            origin: searchParams.origin,
            destination: searchParams.destination,
            departureDate: searchParams.date,
            tripType: searchParams.returnDate ? 'round-trip' : 'one-way',
            returnDate: searchParams.returnDate
          }}
        />
      </section>

      {/* Search Summary */}
      <section className="search-section">
        <div style={{ padding: '24px' }}>
          <h2 style={{ 
            fontSize: '18px', 
            fontWeight: '500', 
            color: '#202124', 
            marginBottom: '16px' 
          }}>
            🔍 Current Search
          </h2>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '16px',
            padding: '16px',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid #e8eaed'
          }}>
            <div>
              <p style={{ 
                fontSize: '12px', 
                color: '#5f6368', 
                margin: '0 0 4px 0',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}>
                From
              </p>
              <p style={{ 
                fontSize: '16px', 
                color: '#202124', 
                margin: 0,
                fontWeight: '500'
              }}>
                {searchParams.origin?.suggestionTitle || searchParams.originSkyId || 'Not selected'}
              </p>
            </div>

            <div>
              <p style={{ 
                fontSize: '12px', 
                color: '#5f6368', 
                margin: '0 0 4px 0',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}>
                To
              </p>
              <p style={{ 
                fontSize: '16px', 
                color: '#202124', 
                margin: 0,
                fontWeight: '500'
              }}>
                {searchParams.destination?.suggestionTitle || searchParams.destinationSkyId || 'Not selected'}
              </p>
            </div>

            <div>
              <p style={{ 
                fontSize: '12px', 
                color: '#5f6368', 
                margin: '0 0 4px 0',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}>
                Departure
              </p>
              <p style={{ 
                fontSize: '16px', 
                color: '#202124', 
                margin: 0,
                fontWeight: '500'
              }}>
                {searchParams.date || 'Not selected'}
              </p>
            </div>

            {searchParams.returnDate && (
              <div>
                <p style={{ 
                  fontSize: '12px', 
                  color: '#5f6368', 
                  margin: '0 0 4px 0',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}>
                  Return
                </p>
                <p style={{ 
                  fontSize: '16px', 
                  color: '#202124', 
                  margin: 0,
                  fontWeight: '500'
                }}>
                  {searchParams.returnDate}
                </p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Results Placeholder */}
      <section className="search-section">
        <div style={{ 
          textAlign: 'center', 
          padding: '48px 24px',
          backgroundColor: '#f8f9fa',
          borderRadius: '12px',
          border: '2px dashed #dadce0'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>✈️</div>
          <h3 style={{ 
            fontSize: '20px', 
            fontWeight: '500', 
            color: '#202124', 
            marginBottom: '8px',
            margin: '0 0 8px 0'
          }}>
            Flight Results Coming Soon!
          </h3>
          <p style={{ 
            fontSize: '14px', 
            color: '#5f6368', 
            margin: 0,
            lineHeight: '1.5'
          }}>
            This is where flight results will be displayed.<br />
            For now, you can practice using the SearchBar above.
          </p>
        </div>
      </section>

      {/* Development Info */}
      <section className="search-section">
        <div style={{ padding: '24px' }}>
          <h3 style={{ 
            fontSize: '16px', 
            fontWeight: '500', 
            color: '#1a73e8', 
            marginBottom: '12px',
            margin: '0 0 12px 0'
          }}>
            🛠️ Development Notes
          </h3>
          <div style={{ 
            padding: '16px',
            backgroundColor: '#e8f0fe',
            borderRadius: '8px',
            border: '1px solid #dadce0'
          }}>
            <ul style={{ 
              fontSize: '14px', 
              color: '#5f6368', 
              margin: 0,
              paddingLeft: '20px',
              lineHeight: '1.6'
            }}>
              <li>✅ Routing implemented with React Router</li>
              <li>✅ Search parameters passed between pages</li>
              <li>✅ SearchBar pre-filled with previous search</li>
              <li>🔮 Flight results display (coming next)</li>
              <li>🔮 Pagination and filtering (future)</li>
            </ul>
          </div>
        </div>
      </section>
    </main>
  );
};

export default SearchPage;
