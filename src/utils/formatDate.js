/**
 * Format date for API requests (YYYY-MM-DD)
 * @param {Date} date - Date object to format
 * @returns {string} Formatted date string
 */
export const formatDateForAPI = (date) => {
  if (!date) return '';
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

/**
 * Format date for display (e.g., "Jul 31, 2025")
 * @param {string|Date} date - Date string or Date object
 * @returns {string} Formatted date string
 */
export const formatDateForDisplay = (date) => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Format time for display (e.g., "2:30 PM")
 * @param {string} dateTimeString - ISO datetime string
 * @returns {string} Formatted time string
 */
export const formatTimeForDisplay = (dateTimeString) => {
  if (!dateTimeString) return '';
  
  const date = new Date(dateTimeString);
  
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
};

/**
 * Get today's date as Date object
 * @returns {Date} Today's date
 */
export const getTodayDate = () => {
  return new Date();
};

/**
 * Get minimum selectable date (today)
 * @returns {Date} Today's date
 */
export const getMinDate = () => {
  return getTodayDate();
};

/**
 * Get maximum selectable date (1 year from today)
 * @returns {Date} Date one year from today
 */
export const getMaxDate = () => {
  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + 1);
  return maxDate;
};
