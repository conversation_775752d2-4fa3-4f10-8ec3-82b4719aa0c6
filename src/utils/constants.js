// API Constants
export const API_ENDPOINTS = {
  SEARCH_AIRPORT: '/v1/flights/searchAirport',
  SEARCH_FLIGHTS: '/v2/flights/searchFlights',
};

// Default search parameters
export const DEFAULT_SEARCH_PARAMS = {
  locale: 'en-US',
  cabinClass: 'economy',
  adults: 1,
  childrens: 0,
  infants: 0,
  sortBy: 'best',
  limit: 20,
  currency: 'USD',
  market: 'en-US',
  countryCode: 'US',
};

// Cabin class options
export const CABIN_CLASSES = [
  { value: 'economy', label: 'Economy' },
  { value: 'premium_economy', label: 'Premium Economy' },
  { value: 'business', label: 'Business' },
  { value: 'first', label: 'First' },
];

// Sort options
export const SORT_OPTIONS = [
  { value: 'best', label: 'Best' },
  { value: 'price_high', label: 'Cheapest' },
  { value: 'fastest', label: 'Fastest' },
  { value: 'outbound_take_off_time', label: 'Outbound Take Off Time' },
  { value: 'outbound_landing_time', label: 'Outbound Landing Time' },
];

// Pagination constants
export const ROWS_PER_PAGE_OPTIONS = [10, 20, 50];
export const DEFAULT_ROWS_PER_PAGE = 10;

// Debounce delay for search input
export const SEARCH_DEBOUNCE_DELAY = 300;
