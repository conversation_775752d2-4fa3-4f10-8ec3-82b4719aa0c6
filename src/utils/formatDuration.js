/**
 * Format duration in minutes to human readable format
 * @param {number} durationInMinutes - Duration in minutes
 * @returns {string} Formatted duration (e.g., "2h 30m", "45m", "1h")
 */
export const formatDuration = (durationInMinutes) => {
  if (!durationInMinutes || durationInMinutes <= 0) return '';
  
  const hours = Math.floor(durationInMinutes / 60);
  const minutes = durationInMinutes % 60;
  
  if (hours === 0) {
    return `${minutes}m`;
  } else if (minutes === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h ${minutes}m`;
  }
};

/**
 * Format stop count to display text
 * @param {number} stopCount - Number of stops
 * @returns {string} Formatted stop text (e.g., "Non-stop", "1 Stop", "2 Stops")
 */
export const formatStops = (stopCount) => {
  if (stopCount === 0) return 'Non-stop';
  if (stopCount === 1) return '1 Stop';
  return `${stopCount} Stops`;
};

/**
 * Calculate layover time between flights
 * @param {string} arrivalTime - Arrival time of first flight
 * @param {string} departureTime - Departure time of connecting flight
 * @returns {string} Formatted layover duration
 */
export const calculateLayoverTime = (arrivalTime, departureTime) => {
  if (!arrivalTime || !departureTime) return '';
  
  const arrival = new Date(arrivalTime);
  const departure = new Date(departureTime);
  
  const layoverMinutes = Math.floor((departure - arrival) / (1000 * 60));
  
  return formatDuration(layoverMinutes);
};

/**
 * Format price with currency symbol
 * @param {number} price - Price amount
 * @param {string} currency - Currency code (default: 'USD')
 * @returns {string} Formatted price (e.g., "$1,234")
 */
export const formatPrice = (price, currency = 'USD') => {
  if (!price) return '';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};
