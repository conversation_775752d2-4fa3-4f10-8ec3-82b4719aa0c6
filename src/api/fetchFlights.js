import apiClient from './axiosConfig.js';
import { API_ENDPOINTS, DEFAULT_SEARCH_PARAMS } from '../utils/constants.js';

/**
 * Fetch flights based on search parameters
 * @param {Object} searchParams - Flight search parameters
 * @returns {Promise<Object>} Flight search results with itineraries and metadata
 */
export const fetchFlights = async (searchParams) => {
  try {
    const {
      originSkyId,
      destinationSkyId,
      originEntityId,
      destinationEntityId,
      date,
      returnDate,
      cabinClass = DEFAULT_SEARCH_PARAMS.cabinClass,
      adults = DEFAULT_SEARCH_PARAMS.adults,
      childrens = DEFAULT_SEARCH_PARAMS.childrens,
      infants = DEFAULT_SEARCH_PARAMS.infants,
      sortBy = DEFAULT_SEARCH_PARAMS.sortBy,
      limit = DEFAULT_SEARCH_PARAMS.limit,
      currency = DEFAULT_SEARCH_PARAMS.currency,
      market = DEFAULT_SEARCH_PARAMS.market,
      countryCode = DEFAULT_SEARCH_PARAMS.countryCode,
    } = searchParams;

    // Validate required parameters
    if (!originSkyId || !destinationSkyId || !originEntityId || !destinationEntityId || !date) {
      throw new Error('Missing required search parameters');
    }

    const params = {
      originSkyId,
      destinationSkyId,
      originEntityId,
      destinationEntityId,
      date,
      cabinClass,
      adults,
      childrens,
      infants,
      sortBy,
      limit,
      currency,
      market,
      countryCode,
    };

    // Add return date if provided (for round trip)
    if (returnDate) {
      params.returnDate = returnDate;
    }

    const response = await apiClient.get(API_ENDPOINTS.SEARCH_FLIGHTS, {
      params,
    });

    if (!response.data?.status || !response.data?.data) {
      throw new Error('Invalid response format from flights API');
    }

    const { data } = response.data;
    
    // Transform flight data for easier consumption
    const transformedFlights = data.itineraries?.map(itinerary => {
      const leg = itinerary.legs?.[0]; // Get first leg for one-way flights
      
      if (!leg) return null;

      return {
        id: itinerary.id,
        price: {
          raw: itinerary.price?.raw || 0,
          formatted: itinerary.price?.formatted || '$0',
        },
        origin: {
          code: leg.origin?.displayCode || '',
          name: leg.origin?.name || '',
          city: leg.origin?.city || '',
          country: leg.origin?.country || '',
        },
        destination: {
          code: leg.destination?.displayCode || '',
          name: leg.destination?.name || '',
          city: leg.destination?.city || '',
          country: leg.destination?.country || '',
        },
        departure: leg.departure,
        arrival: leg.arrival,
        duration: leg.durationInMinutes || 0,
        stops: leg.stopCount || 0,
        carriers: leg.carriers?.marketing || [],
        segments: leg.segments || [],
        farePolicy: itinerary.farePolicy || {},
        score: itinerary.score || 0,
      };
    }).filter(Boolean) || [];

    return {
      flights: transformedFlights,
      context: data.context || {},
      filterStats: data.filterStats || {},
      totalResults: data.context?.totalResults || 0,
    };
  } catch (error) {
    console.error('Error fetching flights:', error);
    throw error;
  }
};
