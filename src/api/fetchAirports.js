import apiClient from './axiosConfig.js';
import { API_ENDPOINTS, DEFAULT_SEARCH_PARAMS } from '../utils/constants.js';

/**
 * Fetch airports based on search query
 * @param {string} query - Search query for airport/city name
 * @returns {Promise<Array>} Array of airport objects
 */
export const fetchAirports = async (query) => {
  try {
    if (!query || query.trim().length < 2) {
      return [];
    }

    const response = await apiClient.get(API_ENDPOINTS.SEARCH_AIRPORT, {
      params: {
        query: query.trim(),
        locale: DEFAULT_SEARCH_PARAMS.locale,
      },
    });

    if (!response.data?.status || !response.data?.data) {
      console.warn('Invalid response format from airport search API');
      return [];
    }

    // Filter only airports (not cities) and transform data
    const airports = response.data.data
      .filter(item => item.navigation?.entityType === 'AIRPORT')
      .map(airport => ({
        skyId: airport.skyId,
        entityId: airport.entityId,
        name: airport.presentation?.title || '',
        suggestionTitle: airport.presentation?.suggestionTitle || '',
        subtitle: airport.presentation?.subtitle || '',
        displayCode: airport.skyId,
        localizedName: airport.navigation?.localizedName || '',
        // Create a combined label for display
        label: `${airport.presentation?.suggestionTitle || airport.presentation?.title || ''} - ${airport.presentation?.subtitle || ''}`,
      }));

    return airports;
  } catch (error) {
    console.error('Error fetching airports:', error);

    // Handle rate limit exceeded (429) with mock data for development
    if (error.response?.status === 429) {
      console.warn('🚨 Rate limit exceeded - using mock data for development');
      return getMockAirports(query);
    }

    // Return empty array on other errors to prevent UI crashes
    return [];
  }
}

/**
 * Mock airport data for development when API quota is exceeded
 * This helps you continue developing without hitting rate limits
 */
const getMockAirports = (query) => {
  const mockData = [
    {
      skyId: 'JFK',
      entityId: '95565058',
      name: 'New York John F. Kennedy',
      suggestionTitle: 'New York John F. Kennedy (JFK)',
      subtitle: 'United States',
      displayCode: 'JFK',
      localizedName: 'New York John F. Kennedy',
      label: 'New York John F. Kennedy (JFK) - United States',
    },
    {
      skyId: 'LHR',
      entityId: '95565050',
      name: 'London Heathrow',
      suggestionTitle: 'London Heathrow (LHR)',
      subtitle: 'United Kingdom',
      displayCode: 'LHR',
      localizedName: 'London Heathrow',
      label: 'London Heathrow (LHR) - United Kingdom',
    },
    {
      skyId: 'LAX',
      entityId: '95565041',
      name: 'Los Angeles International',
      suggestionTitle: 'Los Angeles International (LAX)',
      subtitle: 'United States',
      displayCode: 'LAX',
      localizedName: 'Los Angeles International',
      label: 'Los Angeles International (LAX) - United States',
    },
    {
      skyId: 'NRT',
      entityId: '95565068',
      name: 'Tokyo Narita',
      suggestionTitle: 'Tokyo Narita (NRT)',
      subtitle: 'Japan',
      displayCode: 'NRT',
      localizedName: 'Tokyo Narita',
      label: 'Tokyo Narita (NRT) - Japan',
    },
  ];

  // Filter mock data based on query
  const filtered = mockData.filter(airport =>
    airport.name.toLowerCase().includes(query.toLowerCase()) ||
    airport.suggestionTitle.toLowerCase().includes(query.toLowerCase()) ||
    airport.displayCode.toLowerCase().includes(query.toLowerCase())
  );

  return filtered;
};

/**
 * Get airport details by skyId
 * @param {string} skyId - Airport sky ID
 * @returns {Promise<Object|null>} Airport object or null if not found
 */
export const getAirportBySkyId = async (skyId) => {
  try {
    if (!skyId) return null;

    // Search for the specific airport
    const airports = await fetchAirports(skyId);
    
    // Find exact match by skyId
    const airport = airports.find(a => a.skyId === skyId);
    
    return airport || null;
  } catch (error) {
    console.error('Error getting airport by skyId:', error);
    return null;
  }
};
