import apiClient from './axiosConfig.js';
import { API_ENDPOINTS, DEFAULT_SEARCH_PARAMS } from '../utils/constants.js';

/**
 * Fetch airports based on search query
 * @param {string} query - Search query for airport/city name
 * @returns {Promise<Array>} Array of airport objects
 */
export const fetchAirports = async (query) => {
  try {
    if (!query || query.trim().length < 2) {
      return [];
    }

    const response = await apiClient.get(API_ENDPOINTS.SEARCH_AIRPORT, {
      params: {
        query: query.trim(),
        locale: DEFAULT_SEARCH_PARAMS.locale,
      },
    });

    if (!response.data?.status || !response.data?.data) {
      console.warn('Invalid response format from airport search API');
      return [];
    }

    // Filter only airports (not cities) and transform data
    const airports = response.data.data
      .filter(item => item.navigation?.entityType === 'AIRPORT')
      .map(airport => ({
        skyId: airport.skyId,
        entityId: airport.entityId,
        name: airport.presentation?.title || '',
        suggestionTitle: airport.presentation?.suggestionTitle || '',
        subtitle: airport.presentation?.subtitle || '',
        displayCode: airport.skyId,
        localizedName: airport.navigation?.localizedName || '',
        // Create a combined label for display
        label: `${airport.presentation?.suggestionTitle || airport.presentation?.title || ''} - ${airport.presentation?.subtitle || ''}`,
      }));

    return airports;
  } catch (error) {
    console.error('Error fetching airports:', error);
    
    // Return empty array on error to prevent UI crashes
    return [];
  }
};

/**
 * Get airport details by skyId
 * @param {string} skyId - Airport sky ID
 * @returns {Promise<Object|null>} Airport object or null if not found
 */
export const getAirportBySkyId = async (skyId) => {
  try {
    if (!skyId) return null;

    // Search for the specific airport
    const airports = await fetchAirports(skyId);
    
    // Find exact match by skyId
    const airport = airports.find(a => a.skyId === skyId);
    
    return airport || null;
  } catch (error) {
    console.error('Error getting airport by skyId:', error);
    return null;
  }
};
