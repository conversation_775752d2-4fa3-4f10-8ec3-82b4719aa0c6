import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import HomePage from "./pages/HomePage.jsx";
import SearchPage from "./pages/SearchPage.jsx";
import "./App.css";

/**
 * Header Component
 *
 * Google Flights header with navigation
 */
const Header = () => {
  const location = useLocation();

  return (
    <header className="header">
      <div className="header-content">
        <Link
          to="/"
          className="logo"
          style={{ textDecoration: 'none' }}
        >
          🌐 Google Flights
        </Link>

        {/* Navigation breadcrumb */}
        <div style={{
          fontSize: '14px',
          color: '#5f6368',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <Link
            to="/"
            style={{
              color: location.pathname === '/' ? '#1a73e8' : '#5f6368',
              textDecoration: 'none',
              fontWeight: location.pathname === '/' ? '500' : '400'
            }}
          >
            Home
          </Link>
          {location.pathname === '/search' && (
            <>
              <span>›</span>
              <span style={{ color: '#1a73e8', fontWeight: '500' }}>
                Search Results
              </span>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

/**
 * Main App Component
 *
 * Router setup with Google Flights header and page routing
 */
function App() {
  return (
    <Router>
      <div>
        <Header />

        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/search" element={<SearchPage />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
