import React, { useState } from 'react';
import {
  Container,
  CssB<PERSON>line,
  ThemeProvider,
  createTheme,
  Box,
  Alert,
  Snackbar,
  Fade,
} from '@mui/material';
import SearchBar from './components/SearchBar.jsx';
import FlightsTable from './components/FlightsTable.jsx';
import LoadingSpinner from './components/LoadingSpinner.jsx';
import { fetchFlights } from './api/fetchFlights.js';

// Create Material-UI theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#667eea',
    },
    secondary: {
      main: '#764ba2',
    },
    background: {
      default: '#f5f7fa',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
  },
});

function App() {
  const [flights, setFlights] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [hasSearched, setHasSearched] = useState(false);

  // Handle flight search
  const handleSearch = async (searchParams) => {
    try {
      setLoading(true);
      setError('');
      setHasSearched(true);

      console.log('Searching flights with params:', searchParams);

      const results = await fetchFlights(searchParams);

      setFlights(results.flights || []);
      setSearchResults(results);

      if (!results.flights || results.flights.length === 0) {
        setError('No flights found for your search criteria. Please try different dates or airports.');
      }
    } catch (err) {
      console.error('Flight search error:', err);
      setError(
        err.message ||
        'Failed to search flights. Please check your API key and try again.'
      );
      setFlights([]);
      setSearchResults(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle flight selection
  const handleSelectFlight = (flight) => {
    console.log('Selected flight:', flight);
    // Here you would typically navigate to booking page or show flight details
    alert(`Selected flight: ${flight.origin.code} → ${flight.destination.code} for ${flight.price.formatted}`);
  };

  // Handle error dismissal
  const handleCloseError = () => {
    setError('');
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{
        minHeight: '100vh',
        backgroundColor: 'background.default',
        pb: 4,
      }}>
        <Container maxWidth="xl" sx={{ pt: 3 }}>
          {/* Search Bar */}
          <SearchBar onSearch={handleSearch} loading={loading} />

          {/* Loading State */}
          {loading && (
            <Fade in timeout={300}>
              <Box>
                <LoadingSpinner variant="search" />
              </Box>
            </Fade>
          )}

          {/* Results */}
          {!loading && hasSearched && (
            <Fade in timeout={500}>
              <Box>
                <FlightsTable
                  flights={flights}
                  loading={loading}
                  totalResults={searchResults?.totalResults || 0}
                  onSelectFlight={handleSelectFlight}
                />
              </Box>
            </Fade>
          )}

          {/* Error Snackbar */}
          <Snackbar
            open={!!error}
            autoHideDuration={6000}
            onClose={handleCloseError}
            anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
          >
            <Alert
              onClose={handleCloseError}
              severity="error"
              sx={{ width: '100%' }}
            >
              {error}
            </Alert>
          </Snackbar>
        </Container>
      </Box>
    </ThemeProvider>
  );
}

export default App;
