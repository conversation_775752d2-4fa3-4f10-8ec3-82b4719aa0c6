import SearchBar from "./components/SearchBar.jsx";
import "./App.css";

function App() {
  // Simple handler for search - just log the search parameters for now
  const handleSearch = (searchParams) => {
    console.log("🔍 Search triggered with params:", searchParams);

    // Show search details in a nice format
    const { originSkyId, destinationSkyId, date } = searchParams;
    alert(`✈️ Flight Search:\n${originSkyId} → ${destinationSkyId}\nDate: ${date}`);
  };

  return (
    <div>
      {/* Google Flights Header */}
      <header className="header">
        <div className="header-content">
          <a href="#" className="logo">
            🌐 Google Flights
          </a>
        </div>
      </header>

      {/* Main Content */}
      <main className="main-container">
        {/* Search Section */}
        <section className="search-section">
          <h1 className="search-title">Flights</h1>
          <SearchBar onSearch={handleSearch} />
        </section>

        {/* Info Panel */}
        <section className="search-section">
          <div style={{ textAlign: 'center', padding: '24px' }}>
            <h2 style={{
              fontSize: '20px',
              fontWeight: '500',
              color: '#1a73e8',
              marginBottom: '8px'
            }}>
              🎯 Focus: SearchBar & AirportAutocomplete
            </h2>
            <p style={{
              fontSize: '14px',
              color: '#5f6368',
              margin: 0
            }}>
              This simplified version focuses only on the search functionality.
              Try searching for airports and see the console logs!
            </p>
          </div>
        </section>
      </main>
    </div>
  );
}

export default App;
