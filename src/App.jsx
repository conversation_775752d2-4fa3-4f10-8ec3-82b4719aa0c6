import React from "react";
import {
  Container,
  CssB<PERSON><PERSON>,
  ThemeProvider,
  createTheme,
  Box,
  Typography,
  Paper,
} from "@mui/material";
import SearchBar from "./components/SearchBar.jsx";

// Create Material-UI theme
const theme = createTheme({
  palette: {
    primary: {
      main: "#667eea",
    },
    secondary: {
      main: "#764ba2",
    },
    background: {
      default: "#f5f7fa",
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: "none",
          fontWeight: 500,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
  },
});

function App() {
  // Simple handler for search - just log the search parameters for now
  const handleSearch = (searchParams) => {
    console.log("🔍 Search triggered with params:", searchParams);

    // For now, just show an alert with the search details
    alert(`Search: ${searchParams.originSkyId} → ${searchParams.destinationSkyId} on ${searchParams.date}`);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          minHeight: "100vh",
          backgroundColor: "background.default",
          py: 4,
        }}
      >
        <Container maxWidth="lg">
          {/* App Title */}
          <Typography
            variant="h3"
            component="h1"
            align="center"
            gutterBottom
            sx={{
              mb: 4,
              color: 'primary.main',
              fontWeight: 'bold'
            }}
          >
            ✈️ Flight Search
          </Typography>

          {/* Search Bar - Our main focus */}
          <SearchBar onSearch={handleSearch} />

          {/* Simple info panel */}
          <Paper
            sx={{
              mt: 4,
              p: 3,
              textAlign: 'center',
              backgroundColor: 'rgba(102, 126, 234, 0.05)'
            }}
          >
            <Typography variant="h6" gutterBottom color="primary">
              🎯 Focus: SearchBar & AirportAutocomplete
            </Typography>
            <Typography variant="body2" color="text.secondary">
              This simplified version focuses only on the search functionality.
              Try searching for airports and see the console logs!
            </Typography>
          </Paper>
        </Container>
      </Box>
    </ThemeProvider>
  );
}

export default App;
